import { NextRequest } from 'next/server'
import { successResponse, errorResponse, serverErrorResponse } from '@/utils/response'
import { exchangeCodeForTokens, createOAuth2Client } from '@/lib/google'
import { createOrUpdateUser, generateToken } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { authRateLimit } from '@/middleware/rateLimit'

export async function POST(request: NextRequest) {
  try {
    console.log('🔄 Google OAuth callback POST started')

    // Test database connection
    try {
      await prisma.$connect()
      console.log('✅ Database connection successful')
    } catch (dbError) {
      console.error('❌ Database connection failed:', dbError)
      return serverErrorResponse('Database connection failed')
    }

    const rateLimitResult = authRateLimit(request)
    if (!rateLimitResult.allowed) {
      console.log('❌ Rate limit exceeded')
      return errorResponse('Too many authentication attempts. Please try again later.', 429)
    }

    const body = await request.json()
    const { code } = body
    console.log('📥 Received OAuth code:', code ? 'Present' : 'Missing')

    if (!code) {
      console.log('❌ No authorization code provided')
      return errorResponse('Authorization code is required')
    }

    console.log('🔄 Exchanging code for tokens...')
    const tokens = await exchangeCodeForTokens(code)
    console.log('✅ Tokens received:', tokens ? 'Success' : 'Failed')
    
    if (!tokens.access_token) {
      console.log('❌ No access token in response')
      return errorResponse('Failed to obtain access token')
    }

    console.log('🔄 Getting user info from Google...')
    const oauth2Client = createOAuth2Client()
    oauth2Client.setCredentials(tokens)

    const { google } = require('googleapis')
    const oauth2 = google.oauth2({ version: 'v2', auth: oauth2Client })
    const userInfo = await oauth2.userinfo.get()

    const googleUser = userInfo.data
    console.log('👤 Google user info:', { email: googleUser.email, name: googleUser.name })

    if (!googleUser.email) {
      console.log('❌ No email from Google user info')
      return errorResponse('Failed to get user email from Google')
    }

    console.log('🔄 Creating/updating user in database...')
    const user = await createOrUpdateUser({
      email: googleUser.email,
      name: googleUser.name || undefined,
      avatar: googleUser.picture || undefined
    })
    console.log('✅ User created/updated:', { id: user.id, email: user.email })


    console.log('🔄 Saving OAuth tokens to database...')
    const expiresAt = tokens.expiry_date ? new Date(tokens.expiry_date) : new Date(Date.now() + 3600 * 1000)

    await prisma.oAuthToken.upsert({
      where: {
        userId_provider: {
          userId: user.id,
          provider: 'google'
        }
      },
      update: {
        accessToken: tokens.access_token,
        refreshToken: tokens.refresh_token || undefined,
        expiresAt,
        scope: tokens.scope || '',
        updatedAt: new Date()
      },
      create: {
        userId: user.id,
        provider: 'google',
        accessToken: tokens.access_token,
        refreshToken: tokens.refresh_token || undefined,
        expiresAt,
        scope: tokens.scope || ''
      }
    })
    console.log('✅ OAuth tokens saved successfully')


    const jwtToken = generateToken(user)

    return successResponse({
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
        avatar: user.avatar,
        isPro: user.isPro
      },
      token: jwtToken,
      googleConnected: true
    }, 'Google authentication successful')

  } catch (error) {
    console.error('❌ Google OAuth callback error:', error)

    // Log specific error details
    if (error instanceof Error) {
      console.error('Error message:', error.message)
      console.error('Error stack:', error.stack)
    }

    // Check if it's a database error
    if (error && typeof error === 'object' && 'code' in error) {
      console.error('Database error code:', error.code)
    }

    return serverErrorResponse(`Google authentication failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const code = searchParams.get('code')
    const error = searchParams.get('error')

    if (error) {
      return errorResponse(`Google OAuth error: ${error}`)
    }

    if (!code) {
      return errorResponse('Authorization code not provided')
    }


    const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:3000'
    const redirectUrl = `${frontendUrl}/?code=${encodeURIComponent(code)}`
    
    return Response.redirect(redirectUrl)

  } catch (error) {
    console.error('Google OAuth callback GET error:', error)
    const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:3000'
    const errorUrl = `${frontendUrl}/?error=${encodeURIComponent('Authentication failed')}`
    return Response.redirect(errorUrl)
  }
}
