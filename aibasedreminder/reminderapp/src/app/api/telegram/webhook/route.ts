import { NextRequest } from 'next/server'
import { successResponse, errorResponse, serverErrorResponse } from '@/utils/response'
import { prisma } from '@/lib/prisma'
import { sendMessage } from '@/lib/telegram'
import { getGoogleServices } from '@/lib/google'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    console.log('📱 Telegram webhook received:', JSON.stringify(body, null, 2))

    // Handle different types of updates
    if (body.message) {
      await handleMessage(body.message)
    } else if (body.callback_query) {
      await handleCallbackQuery(body.callback_query)
    }

    return successResponse({ received: true }, 'Webhook processed successfully')
  } catch (error) {
    console.error('❌ Telegram webhook error:', error)
    return serverErrorResponse('Webhook processing failed')
  }
}

export async function handleMessage(message: any) {
  const chatId = message.chat.id
  const text = message.text?.trim()
  const userId = message.from.id

  console.log(`📨 Processing message from ${userId}: "${text}"`)

  if (!text) return

  try {
    if (text === '/start') {
      await sendMessage(chatId, 
        `🤖 Welcome to ReminderAPP Bot!\n\n` +
        `I can help you receive instant notifications for:\n` +
        `📧 New emails\n` +
        `📅 Calendar events\n` +
        `⏰ Custom reminders\n\n` +
        `To get started:\n` +
        `1. Sign in to your ReminderAPP dashboard\n` +
        `2. Go to Settings → Telegram\n` +
        `3. Generate a link code\n` +
        `4. Send me: /link YOUR_CODE\n\n` +
        `Commands:\n` +
        `/start - Show this welcome message\n` +
        `/link CODE - Link your account\n` +
        `/status - Check your account status\n` +
        `/emails - View recent emails\n` +
        `/reply - Reply to an email\n` +
        `/disconnect - Disconnect your account\n` +
        `/help - Show available commands`
      )
    } else if (text === '/help') {
      await sendMessage(chatId,
        `🆘 Available Commands:\n\n` +
        `/start - Welcome message and setup guide\n` +
        `/link CODE - Link your Telegram to ReminderAPP\n` +
        `/status - Check your account linking status\n` +
        `/emails - View your recent emails\n` +
        `/reply - Reply to an email\n` +
        `/disconnect - Disconnect your linked account\n` +
        `/help - Show this help message\n\n` +
        `📧 Email Management:\n` +
        `• Get instant notifications for new emails\n` +
        `• View and categorize your emails\n` +
        `• Reply directly from Telegram\n\n` +
        `Need more help? Visit your ReminderAPP dashboard for detailed instructions.`
      )
    } else if (text === '/status') {
      const telegramUser = await prisma.telegramUser.findUnique({
        where: { telegramId: userId.toString() },
        include: { user: true }
      })

      if (telegramUser) {
        await sendMessage(chatId,
          `✅ Account Status: LINKED\n\n` +
          `📧 Email: ${telegramUser.user.email}\n` +
          `👤 Name: ${telegramUser.user.name || 'Not set'}\n` +
          `🔗 Linked: ${telegramUser.createdAt.toLocaleDateString()}\n` +
          `📱 Active: ${telegramUser.isActive ? 'Yes' : 'No'}\n\n` +
          `You'll receive notifications for new emails and reminders!`
        )
      } else {
        await sendMessage(chatId,
          `❌ Account Status: NOT LINKED\n\n` +
          `To link your account:\n` +
          `1. Go to ReminderAPP dashboard\n` +
          `2. Navigate to Settings → Telegram\n` +
          `3. Generate a link code\n` +
          `4. Send me: /link YOUR_CODE`
        )
      }
    } else if (text === '/disconnect') {
      // Handle disconnect command
      const telegramUser = await prisma.telegramUser.findUnique({
        where: { telegramId: userId.toString() },
        include: { user: true }
      })

      if (!telegramUser) {
        await sendMessage(chatId,
          `❌ No account linked!\n\n` +
          `You don't have any account linked to this Telegram.\n` +
          `Use /link CODE to link your account first.`
        )
        return
      }

      // Send confirmation message with inline keyboard
      const confirmationMessage =
        `⚠️ Disconnect Account?\n\n` +
        `📧 Email: ${telegramUser.user.email}\n` +
        `👤 Name: ${telegramUser.user.name || 'Not set'}\n\n` +
        `Are you sure you want to disconnect this account?\n` +
        `You'll stop receiving notifications until you link again.`

      const keyboard = {
        inline_keyboard: [
          [
            { text: "✅ Yes, Disconnect", callback_data: `disconnect_confirm_${telegramUser.id}` },
            { text: "❌ Cancel", callback_data: "disconnect_cancel" }
          ]
        ]
      }

      await sendMessage(chatId, confirmationMessage, { replyMarkup: keyboard })
    } else if (text.startsWith('/link ')) {
      const code = text.replace('/link ', '').trim().toUpperCase()
      
      if (code.length !== 8) {
        await sendMessage(chatId,
          `❌ Invalid link code format!\n\n` +
          `Please use: /link YOUR_8_DIGIT_CODE\n` +
          `Example: /link ABC12345`
        )
        return
      }

      // Find the link code in database
      const linkCode = await prisma.telegramLinkCode.findUnique({
        where: { code },
        include: { user: true }
      })

      if (!linkCode) {
        await sendMessage(chatId,
          `❌ Invalid or expired link code!\n\n` +
          `Please generate a new code from your ReminderAPP dashboard.`
        )
        return
      }

      if (linkCode.isUsed) {
        await sendMessage(chatId,
          `❌ This link code has already been used!\n\n` +
          `Please generate a new code from your dashboard.`
        )
        return
      }

      if (linkCode.expiresAt < new Date()) {
        await sendMessage(chatId,
          `❌ This link code has expired!\n\n` +
          `Please generate a new code from your dashboard.`
        )
        return
      }

      // Check if user is already linked
      const existingTelegramUser = await prisma.telegramUser.findUnique({
        where: { telegramId: userId.toString() }
      })

      if (existingTelegramUser) {
        await sendMessage(chatId,
          `⚠️ Your Telegram is already linked to another account!\n\n` +
          `If you want to link to a different account, please contact support.`
        )
        return
      }

      // Create the Telegram user link
      await prisma.telegramUser.create({
        data: {
          userId: linkCode.userId,
          telegramId: userId.toString(),
          username: message.from.username || null,
          firstName: message.from.first_name,
          lastName: message.from.last_name || null,
          isActive: true
        }
      })

      // Mark the link code as used
      await prisma.telegramLinkCode.update({
        where: { id: linkCode.id },
        data: { isUsed: true }
      })

      await sendMessage(chatId,
        `🎉 Successfully linked your Telegram account!\n\n` +
        `📧 Email: ${linkCode.user.email}\n` +
        `👤 Name: ${linkCode.user.name || 'Not set'}\n\n` +
        `You'll now receive instant notifications for:\n` +
        `📧 New emails\n` +
        `📅 Calendar events\n` +
        `⏰ Custom reminders\n\n` +
        `Welcome to ReminderAPP! 🚀`
      )

      console.log(`✅ Successfully linked Telegram user ${userId} to account ${linkCode.user.email}`)
    } else if (text === '/emails') {
      await handleEmailsCommand(chatId, userId)
    } else if (text === '/reply') {
      await handleReplyCommand(chatId, userId)
    } else if (text.startsWith('reply:')) {
      await handleEmailReply(chatId, userId, text)
    } else {
      await sendMessage(chatId,
        `🤔 I don't understand that command.\n\n` +
        `Try one of these:\n` +
        `/start - Get started\n` +
        `/help - Show available commands\n` +
        `/status - Check account status\n` +
        `/disconnect - Disconnect account\n` +
        `/link CODE - Link your account`
      )
    }
  } catch (error) {
    console.error('❌ Error handling message:', error)
    await sendMessage(chatId,
      `❌ Sorry, something went wrong processing your request.\n\n` +
      `Please try again in a moment.`
    )
  }
}

export async function handleCallbackQuery(callbackQuery: any) {
  const chatId = callbackQuery.message.chat.id
  const data = callbackQuery.data
  const userId = callbackQuery.from.id

  console.log(`🔘 Processing callback query: ${data}`)

  try {
    if (data === 'disconnect_cancel') {
      await sendMessage(chatId,
        `✅ Disconnect cancelled.\n\n` +
        `Your account remains linked and active.`
      )
    } else if (data.startsWith('disconnect_confirm_')) {
      const telegramUserId = data.replace('disconnect_confirm_', '')

      // Find and delete the telegram user connection
      const telegramUser = await prisma.telegramUser.findUnique({
        where: { id: telegramUserId },
        include: { user: true }
      })

      if (!telegramUser) {
        await sendMessage(chatId,
          `❌ Account connection not found.\n\n` +
          `It may have already been disconnected.`
        )
        return
      }

      // Delete the telegram user connection
      await prisma.telegramUser.delete({
        where: { id: telegramUserId }
      })

      await sendMessage(chatId,
        `✅ Account Disconnected Successfully!\n\n` +
        `📧 ${telegramUser.user.email} has been disconnected from this Telegram.\n\n` +
        `You'll no longer receive notifications.\n` +
        `To reconnect, use /link with a new code from your dashboard.`
      )

      console.log(`✅ Successfully disconnected Telegram user ${userId} from account ${telegramUser.user.email}`)
    } else if (data.startsWith('mark_read_')) {
      const emailId = data.replace('mark_read_', '')
      await handleMarkEmailAsRead(chatId, userId, emailId)
    } else if (data.startsWith('star_email_')) {
      const emailId = data.replace('star_email_', '')
      await handleStarEmail(chatId, userId, emailId)
    } else if (data.startsWith('stop_reminders_')) {
      const emailId = data.replace('stop_reminders_', '')
      await handleStopEmailReminders(chatId, userId, emailId)
    } else {
      await sendMessage(chatId, `Callback received: ${data}`)
    }
  } catch (error) {
    console.error('❌ Error handling callback query:', error)
    await sendMessage(chatId,
      `❌ Sorry, something went wrong processing your request.\n\n` +
      `Please try again in a moment.`
    )
  }
}

async function handleMarkEmailAsRead(chatId: string, telegramUserId: number, emailId: string) {
  try {
    // Find the telegram user and associated email
    const telegramUser = await prisma.telegramUser.findUnique({
      where: { telegramId: telegramUserId.toString() },
      include: { user: true }
    })

    if (!telegramUser) {
      await sendMessage(chatId, `❌ Account not linked. Use /link to connect your account.`)
      return
    }

    const email = await prisma.email.findFirst({
      where: {
        id: emailId,
        userId: telegramUser.userId
      }
    })

    if (!email) {
      await sendMessage(chatId, `❌ Email not found or access denied.`)
      return
    }

    // Mark email as read in database
    await prisma.email.update({
      where: { id: emailId },
      data: { isRead: true }
    })

    // Cancel any pending reminders for this email
    await prisma.reminder.updateMany({
      where: {
        emailId: emailId,
        status: 'PENDING'
      },
      data: {
        status: 'CANCELLED'
      }
    })

    await sendMessage(chatId,
      `✅ Email marked as read!\n\n` +
      `📧 "${email.subject}"\n\n` +
      `All reminders for this email have been stopped.`
    )

    console.log(`✅ Email ${emailId} marked as read by Telegram user ${telegramUserId}`)
  } catch (error) {
    console.error('Error marking email as read:', error)
    await sendMessage(chatId, `❌ Failed to mark email as read. Please try again.`)
  }
}

async function handleStarEmail(chatId: string, telegramUserId: number, emailId: string) {
  try {
    const telegramUser = await prisma.telegramUser.findUnique({
      where: { telegramId: telegramUserId.toString() },
      include: { user: true }
    })

    if (!telegramUser) {
      await sendMessage(chatId, `❌ Account not linked. Use /link to connect your account.`)
      return
    }

    const email = await prisma.email.findFirst({
      where: {
        id: emailId,
        userId: telegramUser.userId
      }
    })

    if (!email) {
      await sendMessage(chatId, `❌ Email not found or access denied.`)
      return
    }

    // Note: This would require Gmail API integration to actually star the email
    // For now, we'll just acknowledge the action
    await sendMessage(chatId,
      `⭐ Star Email Action\n\n` +
      `📧 "${email.subject}"\n\n` +
      `To star this email, please open Gmail directly.\n` +
      `Gmail integration for starring emails will be added in a future update.`
    )

    console.log(`⭐ Star email request for ${emailId} by Telegram user ${telegramUserId}`)
  } catch (error) {
    console.error('Error starring email:', error)
    await sendMessage(chatId, `❌ Failed to star email. Please try again.`)
  }
}

async function handleStopEmailReminders(chatId: string, telegramUserId: number, emailId: string) {
  try {
    const telegramUser = await prisma.telegramUser.findUnique({
      where: { telegramId: telegramUserId.toString() },
      include: { user: true }
    })

    if (!telegramUser) {
      await sendMessage(chatId, `❌ Account not linked. Use /link to connect your account.`)
      return
    }

    const email = await prisma.email.findFirst({
      where: {
        id: emailId,
        userId: telegramUser.userId
      }
    })

    if (!email) {
      await sendMessage(chatId, `❌ Email not found or access denied.`)
      return
    }

    // Cancel all pending reminders for this email
    const cancelledReminders = await prisma.reminder.updateMany({
      where: {
        emailId: emailId,
        status: 'PENDING'
      },
      data: {
        status: 'CANCELLED'
      }
    })

    await sendMessage(chatId,
      `🔕 Reminders Stopped!\n\n` +
      `📧 "${email.subject}"\n\n` +
      `Cancelled ${cancelledReminders.count} pending reminder(s).\n` +
      `You won't receive more notifications for this email.`
    )

    console.log(`🔕 Stopped ${cancelledReminders.count} reminders for email ${emailId} by Telegram user ${telegramUserId}`)
  } catch (error) {
    console.error('Error stopping email reminders:', error)
    await sendMessage(chatId, `❌ Failed to stop reminders. Please try again.`)
  }
}

// Handle /emails command - show recent emails
async function handleEmailsCommand(chatId: number, userId: number) {
  try {
    const telegramUser = await prisma.telegramUser.findUnique({
      where: { telegramId: userId.toString() },
      include: { user: true }
    })

    if (!telegramUser) {
      await sendMessage(chatId.toString(),
        `❌ Account not linked!\n\n` +
        `Please link your account first using /link CODE`
      )
      return
    }

    // Get recent emails
    const emails = await prisma.email.findMany({
      where: { userId: telegramUser.userId },
      orderBy: { receivedAt: 'desc' },
      take: 10
    })

    if (emails.length === 0) {
      await sendMessage(chatId.toString(),
        `📭 No emails found!\n\n` +
        `Your recent emails will appear here once they're synced.`
      )
      return
    }

    let message = `📧 Your Recent Emails (${emails.length}):\n\n`

    emails.forEach((email, index) => {
      const status = email.isRead ? '✅' : '📩'
      const date = email.receivedAt.toLocaleDateString()
      message += `${status} ${index + 1}. ${email.subject}\n`
      message += `   From: ${email.from}\n`
      message += `   Date: ${date}\n`
      if (!email.isRead) {
        message += `   📱 Reply: /reply ${email.id}\n`
      }
      message += `\n`
    })

    message += `💡 Use /reply <email_id> to respond to any email!`

    await sendMessage(chatId.toString(), message)
  } catch (error) {
    console.error('Error handling emails command:', error)
    await sendMessage(chatId.toString(), `❌ Failed to fetch emails. Please try again.`)
  }
}

// Handle /reply command - show instructions for replying
async function handleReplyCommand(chatId: number, userId: number) {
  try {
    const telegramUser = await prisma.telegramUser.findUnique({
      where: { telegramId: userId.toString() },
      include: { user: true }
    })

    if (!telegramUser) {
      await sendMessage(chatId.toString(),
        `❌ Account not linked!\n\n` +
        `Please link your account first using /link CODE`
      )
      return
    }

    // Get recent unread emails
    const unreadEmails = await prisma.email.findMany({
      where: {
        userId: telegramUser.userId,
        isRead: false
      },
      orderBy: { receivedAt: 'desc' },
      take: 5
    })

    if (unreadEmails.length === 0) {
      await sendMessage(chatId.toString(),
        `📭 No unread emails to reply to!\n\n` +
        `Use /emails to see all your recent emails.`
      )
      return
    }

    let message = `📧 Reply to Recent Emails:\n\n`

    unreadEmails.forEach((email, index) => {
      message += `📩 ${index + 1}. ${email.subject}\n`
      message += `   From: ${email.from}\n`
      message += `   📱 Reply: reply:${email.id}:Your message here\n\n`
    })

    message += `💡 To reply, type:\n`
    message += `reply:<email_id>:Your response message\n\n`
    message += `Example:\n`
    message += `reply:${unreadEmails[0].id}:Thank you for your email!`

    await sendMessage(chatId.toString(), message)
  } catch (error) {
    console.error('Error handling reply command:', error)
    await sendMessage(chatId.toString(), `❌ Failed to show reply options. Please try again.`)
  }
}

// Handle email reply - send reply via Gmail
async function handleEmailReply(chatId: number, userId: number, text: string) {
  try {
    const telegramUser = await prisma.telegramUser.findUnique({
      where: { telegramId: userId.toString() },
      include: { user: true }
    })

    if (!telegramUser) {
      await sendMessage(chatId.toString(),
        `❌ Account not linked!\n\n` +
        `Please link your account first using /link CODE`
      )
      return
    }

    // Parse the reply format: reply:emailId:message
    const parts = text.split(':')
    if (parts.length < 3) {
      await sendMessage(chatId.toString(),
        `❌ Invalid reply format!\n\n` +
        `Use: reply:<email_id>:Your message\n` +
        `Example: reply:123:Thank you for your email!`
      )
      return
    }

    const emailId = parts[1]
    const replyMessage = parts.slice(2).join(':').trim()

    if (!replyMessage) {
      await sendMessage(chatId.toString(),
        `❌ Reply message cannot be empty!\n\n` +
        `Please include your message after the email ID.`
      )
      return
    }

    // Get the email
    const email = await prisma.email.findFirst({
      where: {
        id: emailId,
        userId: telegramUser.userId
      }
    })

    if (!email) {
      await sendMessage(chatId.toString(),
        `❌ Email not found!\n\n` +
        `Please check the email ID and try again.`
      )
      return
    }

    // Send the reply via Gmail API
    const { gmail } = await getGoogleServices(telegramUser.userId)

    await sendMessage(chatId.toString(),
      `📤 Sending your reply...\n\n` +
      `To: ${email.from}\n` +
      `Subject: Re: ${email.subject}\n` +
      `Message: ${replyMessage}`
    )

    try {
      await gmail.sendReply(email.gmailMessageId, email.threadId || email.gmailMessageId, {
        to: [email.from],
        subject: `Re: ${email.subject}`,
        body: replyMessage
      })

      // Mark email as read
      await prisma.email.update({
        where: { id: emailId },
        data: { isRead: true }
      })

      await sendMessage(chatId.toString(),
        `✅ Reply sent successfully!\n\n` +
        `📧 Your response has been sent to ${email.from}\n` +
        `📝 Subject: Re: ${email.subject}\n\n` +
        `The email has been marked as read.`
      )
    } catch (replyError) {
      console.error('Error sending reply:', replyError)
      await sendMessage(chatId.toString(),
        `❌ Failed to send reply!\n\n` +
        `Error: ${replyError instanceof Error ? replyError.message : 'Unknown error'}\n` +
        `Please try again or reply directly in Gmail.`
      )
    }

  } catch (error) {
    console.error('Error handling email reply:', error)
    await sendMessage(chatId.toString(), `❌ Failed to send reply. Please try again.`)
  }
}

export async function GET() {
  return successResponse({ status: 'Telegram webhook endpoint is active' }, 'Webhook endpoint ready')
}
