import { NextRequest } from 'next/server'
import { successResponse, errorResponse, serverErrorResponse } from '@/utils/response'
import { requireGoogle } from '@/middleware/protected'
import { apiRateLimit } from '@/middleware/rateLimit'
import { getGoogleServices } from '@/lib/google'
import { prisma } from '@/lib/prisma'
import { sendReminderNotification } from '@/lib/telegram'

export const POST = requireGoogle(async (request: NextRequest, user) => {
  try {
    const rateLimitResult = apiRateLimit(request)
    if (!rateLimitResult.allowed) {
      return errorResponse('Rate limit exceeded', 429)
    }

    const body = await request.json().catch(() => ({}))
    const {
      query = 'is:unread',
      maxResults = 20, // Reduced from 100 to 20 for faster sync
      fullSync = false,
      createReminders = true,
      syncLabels = ['INBOX', 'IMPORTANT']
    } = body

    const { gmail } = await getGoogleServices(user.id)

    let syncStats = {
      totalGmailMessages: 0,
      created: 0,
      updated: 0,
      skipped: 0,
      remindersCreated: 0,
      errors: [] as Array<{ messageId: string; error: string }>
    }

    if (fullSync) {
      await prisma.email.deleteMany({
        where: { userId: user.id }
      })
    }

    const gmailMessages = await gmail.getMessages(query, maxResults)
    syncStats.totalGmailMessages = gmailMessages.length

    for (const gmailMessage of gmailMessages) {
      try {
        const headers = gmailMessage.payload?.headers || []
        const subject = headers.find((h: any) => h.name === 'Subject')?.value || 'No Subject'
        const from = headers.find((h: any) => h.name === 'From')?.value || 'Unknown Sender'
        const to = headers.find((h: any) => h.name === 'To')?.value?.split(',').map((email: string) => email.trim()) || []
        const cc = headers.find((h: any) => h.name === 'Cc')?.value?.split(',').map((email: string) => email.trim()) || []
        const receivedAt = new Date(parseInt(gmailMessage.internalDate))

        let body = ''
        if (gmailMessage.payload?.body?.data) {
          body = Buffer.from(gmailMessage.payload.body.data, 'base64').toString()
        } else if (gmailMessage.payload?.parts) {
          const textPart = gmailMessage.payload.parts.find((part: any) => part.mimeType === 'text/plain')
          if (textPart?.body?.data) {
            body = Buffer.from(textPart.body.data, 'base64').toString()
          }
        }

        const isRead = !gmailMessage.labelIds?.includes('UNREAD')
        const isImportant = gmailMessage.labelIds?.includes('IMPORTANT') || false
        const hasAttachments = gmailMessage.payload?.parts?.some((part: any) => part.filename) || false

        const emailData = {
          userId: user.id,
          gmailMessageId: gmailMessage.id,
          threadId: gmailMessage.threadId,
          subject,
          from,
          to,
          cc,
          body,
          isRead,
          isImportant,
          hasAttachments,
          receivedAt
        }

        const existingEmail = await prisma.email.findUnique({
          where: { gmailMessageId: gmailMessage.id }
        })

        let dbEmail
        if (existingEmail) {
          dbEmail = await prisma.email.update({
            where: { gmailMessageId: gmailMessage.id },
            data: {
              ...emailData,
              updatedAt: new Date()
            }
          })
          syncStats.updated++
        } else {
          dbEmail = await prisma.email.create({
            data: emailData
          })
          syncStats.created++

          // Send immediate notification for new unread emails
          if (!isRead) {
            console.log(`📧 Sending immediate notification for new email: ${subject}`)

            const notificationSent = await sendReminderNotification(user.id, {
              id: `email-${dbEmail.id}`,
              title: isImportant ? `🚨 Important Email: ${subject}` : `📧 New Email: ${subject}`,
              description: `From: ${from}\n\n${body.substring(0, 200)}${body.length > 200 ? '...' : ''}`,
              type: 'email',
              scheduledFor: new Date()
            })

            if (notificationSent) {
              await prisma.email.update({
                where: { id: dbEmail.id },
                data: { reminderSent: true }
              })
              console.log(`✅ Immediate notification sent for: ${subject}`)
            }
          }
        }

        if (createReminders && !isRead && !existingEmail) {
          const reminderCount = await createEmailReminders(dbEmail)
          syncStats.remindersCreated += reminderCount
        }

      } catch (error) {
        console.error(`Error syncing email ${gmailMessage.id}:`, error)
        syncStats.errors.push({
          messageId: gmailMessage.id,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }

    await prisma.user.update({
      where: { id: user.id },
      data: { updatedAt: new Date() }
    })

    return successResponse({
      syncStats,
      query,
      maxResults,
      fullSync,
      createReminders
    }, 'Email sync completed successfully')

  } catch (error) {
    console.error('Email sync error:', error)
    return serverErrorResponse('Failed to sync emails')
  }
})

export const GET = requireGoogle(async (request: NextRequest, user) => {
  try {
    const { searchParams } = new URL(request.url)
    const includeStats = searchParams.get('stats') === 'true'

    const lastSyncTime = await prisma.user.findUnique({
      where: { id: user.id },
      select: { updatedAt: true }
    })

    let stats = null
    if (includeStats) {
      const [totalEmails, unreadEmails, importantEmails, emailsWithReminders] = await Promise.all([
        prisma.email.count({ where: { userId: user.id } }),
        prisma.email.count({
          where: {
            userId: user.id,
            isRead: false
          }
        }),
        prisma.email.count({
          where: {
            userId: user.id,
            isImportant: true
          }
        }),
        prisma.email.count({
          where: {
            userId: user.id,
            reminders: {
              some: {}
            }
          }
        })
      ])

      stats = {
        totalEmails,
        unreadEmails,
        importantEmails,
        emailsWithReminders
      }
    }

    return successResponse({
      lastSyncTime: lastSyncTime?.updatedAt,
      stats
    }, 'Email sync status retrieved successfully')

  } catch (error) {
    console.error('Get email sync status error:', error)
    return serverErrorResponse('Failed to get email sync status')
  }
})

async function createEmailReminders(email: any): Promise<number> {
  try {
    const now = new Date()
    const emailAge = now.getTime() - new Date(email.receivedAt).getTime()
    const hoursOld = emailAge / (1000 * 60 * 60)

    const reminders = []

    // Send immediate notifications for all new emails
    if (!email.reminderSent) {
      reminders.push({
        userId: email.userId,
        emailId: email.id,
        type: 'EMAIL' as const,
        title: email.isImportant ? `🚨 Important email: ${email.subject}` : `📧 New email: ${email.subject}`,
        description: `You have a ${email.isImportant ? 'important ' : ''}new email from ${email.from}`,
        scheduledFor: new Date(now.getTime() + 5 * 1000) // Send in 5 seconds for immediate notification
      })
    }

    if (reminders.length > 0) {
      await prisma.reminder.createMany({
        data: reminders
      })

      // Mark email as having reminder sent
      await prisma.email.update({
        where: { id: email.id },
        data: { reminderSent: true }
      })
    }

    return reminders.length
  } catch (error) {
    console.error('Error creating email reminders:', error)
    return 0
  }
}
