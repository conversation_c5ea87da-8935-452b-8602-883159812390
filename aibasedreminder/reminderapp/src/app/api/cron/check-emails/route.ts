import { NextRequest } from 'next/server'
import { successResponse, errorResponse, serverErrorResponse } from '@/utils/response'
import { prisma } from '@/lib/prisma'
import { getGoogleServices } from '@/lib/google'
import { sendReminderNotification } from '@/lib/telegram'

export async function GET(request: NextRequest) {
  try {
    console.log('🔄 Starting scheduled email check for all users...')

    // Add CORS headers for Vercel cron
    const headers = {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    }

    // Get all users with valid Google OAuth tokens
    const users = await prisma.user.findMany({
      where: {
        oAuthTokens: {
          some: {
            provider: 'google',
            expiresAt: {
              gt: new Date()
            }
          }
        }
      },
      include: {
        telegramUsers: true,
        oAuthTokens: {
          where: {
            provider: 'google'
          }
        }
      }
    })

    console.log(`👥 Found ${users.length} users with valid Google tokens`)
    
    let totalNewEmails = 0
    let totalNotifications = 0

    for (const user of users) {
      try {
        console.log(`📧 Checking emails for user: ${user.email}`)
        
        // Skip if user has no Telegram connection
        if (user.telegramUsers.length === 0) {
          console.log(`⏭️ Skipping ${user.email} - no Telegram connection`)
          continue
        }

        const { gmail } = await getGoogleServices(user.id)
        
        // Get recent emails (last 10 minutes for new ones, plus check all unread for reminders)
        const tenMinutesAgo = new Date(Date.now() - 10 * 60 * 1000)
        const newEmailsQuery = `is:unread after:${Math.floor(tenMinutesAgo.getTime() / 1000)}`
        const allUnreadQuery = `is:unread`

        // Get new emails first
        const newGmailMessages = await gmail.getMessages(newEmailsQuery, 20)
        // Get all unread emails for reminder checking
        const allUnreadMessages = await gmail.getMessages(allUnreadQuery, 50)
        console.log(`📨 Found ${newGmailMessages.length} new emails and ${allUnreadMessages.length} total unread emails for ${user.email}`)

        let userNewEmails = 0
        let userNotifications = 0

        // Process new emails first
        for (const gmailMessage of newGmailMessages) {
          try {
            // Check if we already have this email
            const existingEmail = await prisma.email.findUnique({
              where: { gmailMessageId: gmailMessage.id }
            })
            
            if (existingEmail) {
              // Check if we need to send recurring reminder (every 5 minutes)
              const lastReminderTime = existingEmail.lastReminderSent
              const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000)
              
              if (!lastReminderTime || lastReminderTime < fiveMinutesAgo) {
                console.log(`⏰ Sending recurring reminder for email: ${existingEmail.subject}`)
                
                // Send recurring reminder
                for (const telegramUser of user.telegramUsers) {
                  await sendReminderNotification(
                    telegramUser.telegramId,
                    `⏰ REMINDER: You still have an unread email!\n\n📧 From: ${existingEmail.sender}\n📝 Subject: ${existingEmail.subject}\n⏰ Received: ${existingEmail.receivedAt.toLocaleString()}\n\n💡 Reply to this message or mark as read in Gmail to stop reminders.`,
                    existingEmail.id
                  )
                  userNotifications++
                }
                
                // Update last reminder time
                await prisma.email.update({
                  where: { id: existingEmail.id },
                  data: { lastReminderSent: new Date() }
                })
              }
              continue
            }
            
            // This is a new email - save it and send notification
            console.log(`✨ New email found: ${gmailMessage.subject}`)
            
            // Categorize email based on Gmail labels
            const labels = gmailMessage.labelIds || []
            const isSpam = labels.includes('SPAM')
            const isImportant = labels.includes('IMPORTANT') || labels.includes('STARRED')
            const isPromotional = labels.includes('CATEGORY_PROMOTIONS')
            const isSocial = labels.includes('CATEGORY_SOCIAL')
            const isUpdates = labels.includes('CATEGORY_UPDATES')

            let category = 'inbox'
            if (isSpam) category = 'spam'
            else if (isPromotional) category = 'promotions'
            else if (isSocial) category = 'social'
            else if (isUpdates) category = 'updates'
            else if (isImportant) category = 'important'

            const newEmail = await prisma.email.create({
              data: {
                userId: user.id,
                gmailMessageId: gmailMessage.id,
                threadId: gmailMessage.threadId || gmailMessage.id,
                subject: gmailMessage.subject || 'No Subject',
                from: gmailMessage.from || 'Unknown Sender',
                to: gmailMessage.to || user.email,
                cc: gmailMessage.cc || '',
                body: gmailMessage.body || '',
                snippet: gmailMessage.snippet || '',
                receivedAt: new Date(gmailMessage.internalDate || Date.now()),
                isRead: false,
                isStarred: labels.includes('STARRED'),
                isImportant: isImportant,
                category: category,
                labels: labels,
                lastReminderSent: new Date()
              }
            })
            
            userNewEmails++
            
            // Send immediate notification to all user's Telegram accounts
            for (const telegramUser of user.telegramUsers) {
              const notificationMessage = `📧 NEW EMAIL RECEIVED!\n\n📧 From: ${gmailMessage.from}\n📝 Subject: ${gmailMessage.subject}\n📄 Preview: ${gmailMessage.snippet}\n⏰ Received: ${new Date().toLocaleString()}\n\n💬 Reply: reply:${newEmail.id}:Your message here\n📱 Example: reply:${newEmail.id}:Thank you for your email!\n\n⏰ I'll remind you every 5 minutes until you read or reply!`

              // Use direct Telegram API call for reliability
              await fetch(`https://api.telegram.org/bot${process.env.TELEGRAM_BOT_TOKEN}/sendMessage`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                  chat_id: telegramUser.telegramId,
                  text: notificationMessage,
                  parse_mode: 'HTML'
                })
              })

              userNotifications++
              console.log(`📱 Sent notification to Telegram user ${telegramUser.telegramId}`)
            }
            
          } catch (emailError) {
            console.error(`❌ Error processing email ${gmailMessage.id}:`, emailError)
          }
        }
        
        // Now process all unread emails for recurring reminders
        for (const gmailMessage of allUnreadMessages) {
          try {
            const existingEmail = await prisma.email.findUnique({
              where: { gmailMessageId: gmailMessage.id }
            })

            if (existingEmail && !existingEmail.isRead) {
              // Check if we need to send recurring reminder (every 5 minutes)
              const lastReminderTime = existingEmail.lastReminderSent
              const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000)

              if (!lastReminderTime || lastReminderTime < fiveMinutesAgo) {
                console.log(`⏰ Sending recurring reminder for email: ${existingEmail.subject}`)

                // Send recurring reminder
                for (const telegramUser of user.telegramUsers) {
                  const reminderMessage = `⏰ REMINDER: You still have an unread email!\n\n📧 From: ${existingEmail.from}\n📝 Subject: ${existingEmail.subject}\n⏰ Received: ${existingEmail.receivedAt.toLocaleString()}\n\n💬 Reply: reply:${existingEmail.id}:Your message here\n📱 Example: reply:${existingEmail.id}:Thanks for reaching out!\n\n📧 Or mark as read in Gmail to stop reminders.`

                  // Use direct Telegram API call for reliability
                  await fetch(`https://api.telegram.org/bot${process.env.TELEGRAM_BOT_TOKEN}/sendMessage`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                      chat_id: telegramUser.telegramId,
                      text: reminderMessage,
                      parse_mode: 'HTML'
                    })
                  })

                  userNotifications++
                  console.log(`⏰ Sent reminder to Telegram user ${telegramUser.telegramId}`)
                }

                // Update last reminder time
                await prisma.email.update({
                  where: { id: existingEmail.id },
                  data: { lastReminderSent: new Date() }
                })
              }
            }
          } catch (emailError) {
            console.error(`❌ Error processing reminder for email ${gmailMessage.id}:`, emailError)
          }
        }

        totalNewEmails += userNewEmails
        totalNotifications += userNotifications

        console.log(`✅ User ${user.email}: ${userNewEmails} new emails, ${userNotifications} notifications sent`)
        
      } catch (userError) {
        console.error(`❌ Error checking emails for user ${user.email}:`, userError)
      }
    }
    
    console.log(`🎉 Email check completed: ${totalNewEmails} new emails, ${totalNotifications} notifications sent`)

    const response = successResponse({
      usersChecked: users.length,
      newEmails: totalNewEmails,
      notificationsSent: totalNotifications,
      timestamp: new Date().toISOString()
    }, 'Email check completed successfully')

    // Add headers for CORS and caching
    Object.entries(headers).forEach(([key, value]) => {
      response.headers.set(key, value)
    })

    return response
    
  } catch (error) {
    console.error('❌ Scheduled email check failed:', error)
    return serverErrorResponse(`Email check failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

// Also allow POST for manual triggers
export const POST = GET
