import { NextRequest } from 'next/server'
import { successResponse, errorResponse, serverErrorResponse } from '@/utils/response'
import { prisma } from '@/lib/prisma'
import { getGoogleServices } from '@/lib/google'
import { sendReminderNotification } from '@/lib/telegram'

export async function GET(request: NextRequest) {
  try {
    console.log('🔄 Starting scheduled email check for all users...')
    
    // Get all users with valid Google OAuth tokens
    const users = await prisma.user.findMany({
      where: {
        oAuthTokens: {
          some: {
            provider: 'google',
            expiresAt: {
              gt: new Date()
            }
          }
        }
      },
      include: {
        telegramUsers: true,
        oAuthTokens: {
          where: {
            provider: 'google'
          }
        }
      }
    })

    console.log(`👥 Found ${users.length} users with valid Google tokens`)
    
    let totalNewEmails = 0
    let totalNotifications = 0

    for (const user of users) {
      try {
        console.log(`📧 Checking emails for user: ${user.email}`)
        
        // Skip if user has no Telegram connection
        if (user.telegramUsers.length === 0) {
          console.log(`⏭️ Skipping ${user.email} - no Telegram connection`)
          continue
        }

        const { gmail } = await getGoogleServices(user.id)
        
        // Get recent unread emails (last 10 minutes to catch new ones)
        const tenMinutesAgo = new Date(Date.now() - 10 * 60 * 1000)
        const query = `is:unread after:${Math.floor(tenMinutesAgo.getTime() / 1000)}`
        
        const gmailMessages = await gmail.getMessages(query, 20)
        console.log(`📨 Found ${gmailMessages.length} recent unread emails for ${user.email}`)
        
        let userNewEmails = 0
        let userNotifications = 0
        
        for (const gmailMessage of gmailMessages) {
          try {
            // Check if we already have this email
            const existingEmail = await prisma.email.findUnique({
              where: { gmailMessageId: gmailMessage.id }
            })
            
            if (existingEmail) {
              // Check if we need to send recurring reminder (every 5 minutes)
              const lastReminderTime = existingEmail.lastReminderSent
              const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000)
              
              if (!lastReminderTime || lastReminderTime < fiveMinutesAgo) {
                console.log(`⏰ Sending recurring reminder for email: ${existingEmail.subject}`)
                
                // Send recurring reminder
                for (const telegramUser of user.telegramUsers) {
                  await sendReminderNotification(
                    telegramUser.telegramId,
                    `⏰ REMINDER: You still have an unread email!\n\n📧 From: ${existingEmail.sender}\n📝 Subject: ${existingEmail.subject}\n⏰ Received: ${existingEmail.receivedAt.toLocaleString()}\n\n💡 Reply to this message or mark as read in Gmail to stop reminders.`,
                    existingEmail.id
                  )
                  userNotifications++
                }
                
                // Update last reminder time
                await prisma.email.update({
                  where: { id: existingEmail.id },
                  data: { lastReminderSent: new Date() }
                })
              }
              continue
            }
            
            // This is a new email - save it and send notification
            console.log(`✨ New email found: ${gmailMessage.subject}`)
            
            const newEmail = await prisma.email.create({
              data: {
                userId: user.id,
                gmailMessageId: gmailMessage.id,
                subject: gmailMessage.subject || 'No Subject',
                sender: gmailMessage.from || 'Unknown Sender',
                snippet: gmailMessage.snippet || '',
                receivedAt: new Date(gmailMessage.internalDate || Date.now()),
                isRead: false,
                labels: gmailMessage.labelIds || [],
                lastReminderSent: new Date()
              }
            })
            
            userNewEmails++
            
            // Send immediate notification to all user's Telegram accounts
            for (const telegramUser of user.telegramUsers) {
              await sendReminderNotification(
                telegramUser.telegramId,
                `📧 NEW EMAIL RECEIVED!\n\n📧 From: ${gmailMessage.from}\n📝 Subject: ${gmailMessage.subject}\n📄 Preview: ${gmailMessage.snippet}\n⏰ Received: ${new Date().toLocaleString()}\n\n💡 I'll remind you every 5 minutes until you read it!`,
                newEmail.id
              )
              userNotifications++
            }
            
          } catch (emailError) {
            console.error(`❌ Error processing email ${gmailMessage.id}:`, emailError)
          }
        }
        
        totalNewEmails += userNewEmails
        totalNotifications += userNotifications
        
        console.log(`✅ User ${user.email}: ${userNewEmails} new emails, ${userNotifications} notifications sent`)
        
      } catch (userError) {
        console.error(`❌ Error checking emails for user ${user.email}:`, userError)
      }
    }
    
    console.log(`🎉 Email check completed: ${totalNewEmails} new emails, ${totalNotifications} notifications sent`)
    
    return successResponse({
      usersChecked: users.length,
      newEmails: totalNewEmails,
      notificationsSent: totalNotifications,
      timestamp: new Date().toISOString()
    }, 'Email check completed successfully')
    
  } catch (error) {
    console.error('❌ Scheduled email check failed:', error)
    return serverErrorResponse(`Email check failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}

// Also allow POST for manual triggers
export const POST = GET
