'use client'

import { useEffect, useState } from 'react'

export function ServiceInitializer() {
  const [initialized, setInitialized] = useState(false)
  const [status, setStatus] = useState<string>('Initializing...')

  useEffect(() => {
    if (initialized) return

    const initializeServices = async () => {
      try {
        console.log('🚀 Initializing services...')
        setStatus('Starting notification scheduler...')
        
        // Initialize notification scheduler
        const schedulerResponse = await fetch('/api/system/scheduler', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ action: 'start' })
        })
        
        if (schedulerResponse.ok) {
          console.log('✅ Notification scheduler started')
        }
        
        setStatus('Initializing Telegram bot...')
        
        // Initialize Telegram bot
        const botResponse = await fetch('/api/telegram/init?restart=true', {
          method: 'POST'
        })
        
        if (botResponse.ok) {
          console.log('✅ Telegram bot initialized')
        }
        
        setStatus('All services ready!')
        console.log('🎉 All services initialized successfully!')
        setInitialized(true)
        
        // Hide the status after 3 seconds
        setTimeout(() => {
          setStatus('')
        }, 3000)
        
      } catch (error) {
        console.error('❌ Error initializing services:', error)
        setStatus('Error initializing services')
        setTimeout(() => {
          setStatus('')
        }, 5000)
      }
    }

    // Initialize services after a short delay
    const timer = setTimeout(initializeServices, 2000)
    return () => clearTimeout(timer)
  }, [initialized])

  if (!status) return null

  return (
    <div className="fixed top-4 right-4 bg-gray-800 text-white px-4 py-2 rounded-lg shadow-lg z-50">
      <div className="flex items-center space-x-2">
        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
        <span className="text-sm">{status}</span>
      </div>
    </div>
  )
}
