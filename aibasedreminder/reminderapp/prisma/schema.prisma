// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(uuid())
  email     String   @unique
  name      String?
  avatar    String?
  isPro     <PERSON>  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  oauthTokens     OAuthToken[]
  calendarEvents  CalendarEvent[]
  emails          Email[]
  reminders       Reminder[]
  telegramUsers   TelegramUser[]
  telegramLinkCodes TelegramLinkCode[]

  @@map("users")
}

model OAuthToken {
  id           String   @id @default(uuid())
  userId       String
  provider     String   // 'google'
  accessToken  String
  refreshToken String?
  expiresAt    DateTime
  scope        String   @default("")
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, provider])
  @@map("oauth_tokens")
}

model CalendarEvent {
  id            String   @id @default(uuid())
  userId        String
  googleEventId String   @unique
  title         String
  description   String?
  startTime     DateTime
  endTime       DateTime
  location      String?
  attendees     String   @default("")
  reminderSent  Boolean  @default(false)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("calendar_events")
}

model Email {
  id               String   @id @default(uuid())
  userId           String
  gmailMessageId   String   @unique
  threadId         String
  subject          String
  from             String
  to               String   @default("")
  cc               String   @default("")
  body             String
  snippet          String?
  isRead           Boolean  @default(false)
  isImportant      Boolean  @default(false)
  hasAttachments   Boolean  @default(false)
  labels           String   @default("") // JSON string of Gmail labels
  receivedAt       DateTime
  category         String?
  reminderCreated  Boolean  @default(false)
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt

  // Relations
  user      User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  reminders Reminder[]

  @@map("emails")
}

model Reminder {
  id                String        @id @default(uuid())
  userId            String
  type              String        // 'MEETING', 'EMAIL', 'CUSTOM'
  title             String
  description       String?
  scheduledFor      DateTime
  status            String        @default("PENDING") // 'PENDING', 'SENT', 'SNOOZED', 'COMPLETED', 'CANCELLED'
  emailId           String?
  calendarEventId   String?
  telegramMessageId String?
  createdAt         DateTime      @default(now())
  updatedAt         DateTime      @updatedAt

  // Relations
  user  User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  email Email? @relation(fields: [emailId], references: [id], onDelete: SetNull)

  @@map("reminders")
}

model TelegramUser {
  id          String   @id @default(uuid())
  userId      String
  telegramId  String   @unique
  username    String?
  firstName   String
  lastName    String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("telegram_users")
}

model TelegramLinkCode {
  id        String   @id @default(uuid())
  userId    String   @unique
  code      String   @unique
  expiresAt DateTime
  isUsed    Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("telegram_link_codes")
}
